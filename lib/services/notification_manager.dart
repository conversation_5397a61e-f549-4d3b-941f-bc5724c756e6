import 'dart:async';
import 'dart:convert';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../config/firebase_config.dart';
import 'firebase_messaging_service.dart';
import 'presence_service.dart';
import 'api_service.dart';
import 'auth_service.dart';

class NotificationManager {
  static final FirebaseMessaging _messaging = FirebaseConfig.messaging;
  static final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();
  
  static String? _currentUserId;
  static String? _currentUserRole;
  static StreamSubscription<RemoteMessage>? _messageSubscription;
  
  // Initialize notification system
  static Future<void> initialize() async {
    try {
      // Get current user info
      final userInfo = await AuthService.getCurrentUserInfo();
      if (userInfo != null) {
        _currentUserId = userInfo['id'];
        _currentUserRole = userInfo['role'];
      }
      
      // Initialize Firebase Messaging
      await FirebaseMessagingService.initialize();
      
      // Get and register FCM token
      final token = await FirebaseMessagingService.getToken();
      if (token != null) {
        await PresenceService.updateFCMToken(token);
        await _registerTokenWithBackend(token);
      }
      
      // Listen to token refresh
      _messaging.onTokenRefresh.listen((newToken) async {
        await PresenceService.updateFCMToken(newToken);
        await _registerTokenWithBackend(newToken);
      });
      
      // Subscribe to role-specific topics
      if (_currentUserRole != null) {
        await _subscribeToRoleTopics();
      }
      
      // Set up message handlers
      _setupMessageHandlers();
      
      print('Notification manager initialized successfully');
    } catch (e) {
      print('Error initializing notification manager: $e');
    }
  }
  
  // Register FCM token with backend
  static Future<void> _registerTokenWithBackend(String token) async {
    try {
      await ApiService.post('/notifications/device', {
        'token': token,
        'platform': _getPlatform(),
        'app_version': '1.0.0', // Get from package info
      });
      print('FCM token registered with backend');
    } catch (e) {
      print('Error registering FCM token with backend: $e');
    }
  }
  
  // Subscribe to role-specific topics
  static Future<void> _subscribeToRoleTopics() async {
    if (_currentUserRole == null) return;
    
    try {
      // Subscribe to general topics
      await FirebaseMessagingService.subscribeToTopic('all_users');
      
      // Subscribe to role-specific topics
      if (_currentUserRole == 'client') {
        await FirebaseMessagingService.subscribeToTopic('clients');
        await FirebaseMessagingService.unsubscribeFromTopic('runners');
      } else if (_currentUserRole == 'agent') {
        await FirebaseMessagingService.subscribeToTopic('runners');
        await FirebaseMessagingService.unsubscribeFromTopic('clients');
        
        // Subscribe to location-based topics if needed
        // await _subscribeToLocationTopics();
      }
      
      print('Subscribed to role topics: $_currentUserRole');
    } catch (e) {
      print('Error subscribing to role topics: $e');
    }
  }
  
  // Setup message handlers
  static void _setupMessageHandlers() {
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      print('Received foreground message: ${message.messageId}');
      _handleForegroundMessage(message);
    });
    
    // Handle background/terminated app messages when tapped
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      print('Message opened app: ${message.messageId}');
      _handleMessageTap(message);
    });
    
    // Handle initial message when app is launched from terminated state
    FirebaseMessaging.getInitialMessage().then((RemoteMessage? message) {
      if (message != null) {
        print('App launched from notification: ${message.messageId}');
        _handleMessageTap(message);
      }
    });
  }
  
  // Handle foreground messages
  static void _handleForegroundMessage(RemoteMessage message) {
    final notificationType = message.data['type'] ?? 'general';
    
    // Show appropriate local notification based on type
    switch (notificationType) {
      case 'errand_update':
        _showErrandNotification(message);
        break;
      case 'payment_update':
        _showPaymentNotification(message);
        break;
      case 'new_errand':
        if (_currentUserRole == 'agent') {
          _showNewErrandNotification(message);
        }
        break;
      case 'runner_assigned':
        if (_currentUserRole == 'client') {
          _showRunnerAssignedNotification(message);
        }
        break;
      case 'location_update':
        // Handle real-time location updates
        _handleLocationUpdate(message);
        break;
      default:
        _showGeneralNotification(message);
    }
  }
  
  // Handle message tap navigation
  static void _handleMessageTap(RemoteMessage message) {
    final data = message.data;
    final type = data['type'] ?? 'general';
    final screen = data['screen'];
    final id = data['id'];
    
    print('Handling message tap - Type: $type, Screen: $screen, ID: $id');
    
    // Navigate based on message type and user role
    switch (type) {
      case 'errand_update':
      case 'new_errand':
        if (_currentUserRole == 'agent') {
          _navigateToScreen('/runner/errands', id);
        } else {
          _navigateToScreen('/customer/bookings', id);
        }
        break;
        
      case 'payment_update':
        if (_currentUserRole == 'agent') {
          _navigateToScreen('/runner/earnings', id);
        } else {
          _navigateToScreen('/customer/bookings', id);
        }
        break;
        
      case 'runner_assigned':
        if (_currentUserRole == 'client') {
          _navigateToScreen('/customer/tracking', id);
        }
        break;
        
      case 'chat_message':
        _navigateToScreen('/chat', id);
        break;
        
      default:
        _navigateToScreen('/home', null);
    }
  }
  
  // Show errand-related notification
  static void _showErrandNotification(RemoteMessage message) {
    _showLocalNotification(
      id: message.hashCode,
      title: message.notification?.title ?? 'Errand Update',
      body: message.notification?.body ?? 'Your errand status has been updated',
      channelId: 'errand_updates',
      channelName: 'Errand Updates',
      importance: Importance.high,
      payload: jsonEncode(message.data),
    );
  }
  
  // Show payment notification
  static void _showPaymentNotification(RemoteMessage message) {
    _showLocalNotification(
      id: message.hashCode,
      title: message.notification?.title ?? 'Payment Update',
      body: message.notification?.body ?? 'Payment status updated',
      channelId: 'payment_updates',
      channelName: 'Payment Updates',
      importance: Importance.max,
      payload: jsonEncode(message.data),
    );
  }
  
  // Show new errand notification (for runners)
  static void _showNewErrandNotification(RemoteMessage message) {
    _showLocalNotification(
      id: message.hashCode,
      title: message.notification?.title ?? 'New Errand Available',
      body: message.notification?.body ?? 'A new errand is available in your area',
      channelId: 'new_errands',
      channelName: 'New Errands',
      importance: Importance.high,
      payload: jsonEncode(message.data),
      sound: 'new_errand_sound', // Custom sound for new errands
    );
  }
  
  // Show runner assigned notification (for clients)
  static void _showRunnerAssignedNotification(RemoteMessage message) {
    _showLocalNotification(
      id: message.hashCode,
      title: message.notification?.title ?? 'Runner Assigned',
      body: message.notification?.body ?? 'A runner has been assigned to your errand',
      channelId: 'runner_updates',
      channelName: 'Runner Updates',
      importance: Importance.high,
      payload: jsonEncode(message.data),
    );
  }
  
  // Show general notification
  static void _showGeneralNotification(RemoteMessage message) {
    _showLocalNotification(
      id: message.hashCode,
      title: message.notification?.title ?? 'TaskRabbit',
      body: message.notification?.body ?? 'You have a new notification',
      channelId: 'general',
      channelName: 'General Notifications',
      importance: Importance.defaultImportance,
      payload: jsonEncode(message.data),
    );
  }
  
  // Handle location updates
  static void _handleLocationUpdate(RemoteMessage message) {
    final data = message.data;
    final runnerId = data['runner_id'];
    final latitude = double.tryParse(data['latitude'] ?? '0');
    final longitude = double.tryParse(data['longitude'] ?? '0');
    
    print('Location update - Runner: $runnerId, Lat: $latitude, Lng: $longitude');
    
    // Update UI or trigger location-based actions
    // This could trigger a stream controller or state management update
  }
  
  // Show local notification
  static Future<void> _showLocalNotification({
    required int id,
    required String title,
    required String body,
    required String channelId,
    required String channelName,
    Importance importance = Importance.defaultImportance,
    String? payload,
    String? sound,
  }) async {
    final androidDetails = AndroidNotificationDetails(
      channelId,
      channelName,
      channelDescription: 'Notifications for $channelName',
      importance: importance,
      priority: Priority.high,
      showWhen: true,
      sound: sound != null ? RawResourceAndroidNotificationSound(sound) : null,
    );
    
    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );
    
    final details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );
    
    await _localNotifications.show(id, title, body, details, payload: payload);
  }
  
  // Navigate to screen
  static void _navigateToScreen(String route, String? id) {
    // This would typically use your navigation system
    print('Navigate to: $route${id != null ? '/$id' : ''}');
    
    // Example: Using GoRouter or Navigator
    // GoRouter.of(context).push('$route${id != null ? '/$id' : ''}');
    // or trigger a navigation event through your state management
  }
  
  // Get platform string
  static String _getPlatform() {
    // This should be detected automatically
    return 'android'; // or 'ios'
  }
  
  // Send targeted notification to specific user
  static Future<void> sendNotificationToUser({
    required String userId,
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    try {
      await ApiService.post('/notifications/send', {
        'user_id': userId,
        'title': title,
        'body': body,
        'data': data ?? {},
      });
      print('Notification sent to user: $userId');
    } catch (e) {
      print('Error sending notification to user: $e');
    }
  }
  
  // Send notification to all runners in area
  static Future<void> notifyRunnersInArea({
    required double latitude,
    required double longitude,
    required double radiusKm,
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    try {
      await ApiService.post('/notifications/runners/nearby', {
        'latitude': latitude,
        'longitude': longitude,
        'radius_km': radiusKm,
        'title': title,
        'body': body,
        'data': data ?? {},
      });
      print('Notification sent to runners in area');
    } catch (e) {
      print('Error sending notification to runners: $e');
    }
  }
  
  // Clean up when user logs out
  static Future<void> cleanup() async {
    try {
      // Unsubscribe from all topics
      if (_currentUserRole != null) {
        await FirebaseMessagingService.unsubscribeFromTopic('all_users');
        await FirebaseMessagingService.unsubscribeFromTopic('clients');
        await FirebaseMessagingService.unsubscribeFromTopic('runners');
      }
      
      // Cancel subscriptions
      await _messageSubscription?.cancel();
      
      // Clear user info
      _currentUserId = null;
      _currentUserRole = null;
      _messageSubscription = null;
      
      print('Notification manager cleaned up');
    } catch (e) {
      print('Error cleaning up notification manager: $e');
    }
  }
  
  // Request notification permissions
  static Future<bool> requestPermissions() async {
    try {
      NotificationSettings settings = await _messaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );
      
      return settings.authorizationStatus == AuthorizationStatus.authorized;
    } catch (e) {
      print('Error requesting notification permissions: $e');
      return false;
    }
  }
  
  // Get notification permission status
  static Future<AuthorizationStatus> getPermissionStatus() async {
    try {
      NotificationSettings settings = await _messaging.getNotificationSettings();
      return settings.authorizationStatus;
    } catch (e) {
      print('Error getting notification permission status: $e');
      return AuthorizationStatus.denied;
    }
  }
}

// Notification types
enum NotificationType {
  errandUpdate,
  paymentUpdate,
  newErrand,
  runnerAssigned,
  locationUpdate,
  chatMessage,
  general,
}

// Notification data model
class AppNotificationData {
  final NotificationType type;
  final String? screen;
  final String? id;
  final Map<String, dynamic> extra;
  
  AppNotificationData({
    required this.type,
    this.screen,
    this.id,
    this.extra = const {},
  });
  
  factory AppNotificationData.fromMap(Map<String, dynamic> map) {
    return AppNotificationData(
      type: NotificationType.values.firstWhere(
        (t) => t.name == map['type'],
        orElse: () => NotificationType.general,
      ),
      screen: map['screen'],
      id: map['id'],
      extra: Map<String, dynamic>.from(map['extra'] ?? {}),
    );
  }
  
  Map<String, dynamic> toMap() {
    return {
      'type': type.name,
      'screen': screen,
      'id': id,
      'extra': extra,
    };
  }
}